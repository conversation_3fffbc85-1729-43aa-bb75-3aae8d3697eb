import 'dart:developer' as developer;

import 'package:drift/drift.dart';

import '../datasources/app_database.dart';
import '../datasources/converters/sync_status_converter.dart';

// Helper function to safely parse DateTime values from various formats
DateTime safeDateTimeParse(String dateStr) {
  try {
    // Try standard ISO 8601 parsing first
    return DateTime.parse(dateStr).toUtc();
  } catch (e) {
    // Log the error
    developer.log('Error parsing date: $dateStr - $e');

    try {
      // Check if it's a Unix timestamp with 'Z' appended
      if (dateStr.endsWith('Z') && dateStr.length > 1) {
        // Remove the 'Z' and try to parse as Unix timestamp (seconds)
        final timestamp = int.parse(dateStr.substring(0, dateStr.length - 1));
        return DateTime.fromMillisecondsSinceEpoch(
          timestamp * 1000,
          isUtc: true,
        );
      }

      // If it's just a Unix timestamp (seconds)
      final timestamp = int.parse(dateStr);
      return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000, isUtc: true);
    } catch (e2) {
      // If all parsing fails, return current time and log error
      developer.log('Failed to parse date after all attempts: $dateStr - $e2');
      return DateTime.now().toUtc();
    }
  }
}

// Base class for all sync models
abstract class SyncModel {
  String get uuid;
  DateTime get updatedAt;
  DateTime? get deletedAt;
  SyncStatus get syncStatus;

  Map<String, dynamic> toJson();

  // Helper method to determine if this record is deleted
  bool get isDeleted => deletedAt != null;
}

// Income Sync Model
class IncomeSyncModel extends SyncModel {
  @override
  final String uuid;
  final DateTime date;
  final int initialMileage;
  final int finalMileage;
  final double initialGopay;
  final double initialBca;
  final double initialCash;
  final double initialOvo;
  final double initialBri;
  final double initialRekpon;
  final double finalGopay;
  final double finalBca;
  final double finalCash;
  final double finalOvo;
  final double finalBri;
  final double finalRekpon;
  final double? initialCapital;
  final double? finalResult;
  final int? mileage;
  final double? netIncome;
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final DateTime? deletedAt;
  @override
  final SyncStatus syncStatus;

  IncomeSyncModel({
    required this.uuid,
    required this.date,
    required this.initialMileage,
    required this.finalMileage,
    required this.initialGopay,
    required this.initialBca,
    required this.initialCash,
    required this.initialOvo,
    required this.initialBri,
    required this.initialRekpon,
    required this.finalGopay,
    required this.finalBca,
    required this.finalCash,
    required this.finalOvo,
    required this.finalBri,
    required this.finalRekpon,
    this.initialCapital,
    this.finalResult,
    this.mileage,
    this.netIncome,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });

  // Factory method to create model from Drift entity
  factory IncomeSyncModel.fromDrift(IncomeData data) {
    return IncomeSyncModel(
      uuid: data.uuid,
      date: data.date,
      initialMileage: data.initialMileage,
      finalMileage: data.finalMileage,
      initialGopay: data.initialGopay,
      initialBca: data.initialBca,
      initialCash: data.initialCash,
      initialOvo: data.initialOvo,
      initialBri: data.initialBri,
      initialRekpon: data.initialRekpon,
      finalGopay: data.finalGopay,
      finalBca: data.finalBca,
      finalCash: data.finalCash,
      finalOvo: data.finalOvo,
      finalBri: data.finalBri,
      finalRekpon: data.finalRekpon,
      initialCapital: data.initialCapital,
      finalResult: data.finalResult,
      mileage: data.mileage,
      netIncome: data.netIncome,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      syncStatus: data.syncStatus,
    );
  }

  // Convert to Supabase JSON format for upload
  @override
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'date': date.toUtc().toIso8601String(),
      'initial_mileage': initialMileage,
      'final_mileage': finalMileage,
      'initial_gopay': initialGopay,
      'initial_bca': initialBca,
      'initial_cash': initialCash,
      'initial_ovo': initialOvo,
      'initial_bri': initialBri,
      'initial_rekpon': initialRekpon,
      'final_gopay': finalGopay,
      'final_bca': finalBca,
      'final_cash': finalCash,
      'final_ovo': finalOvo,
      'final_bri': finalBri,
      'final_rekpon': finalRekpon,
      'initial_capital': initialCapital,
      'final_result': finalResult,
      'mileage': mileage,
      'net_income': netIncome,
      'created_at': createdAt.toUtc().toIso8601String(),
      'updated_at': updatedAt.toUtc().toIso8601String(),
      'deleted_at': deletedAt?.toUtc().toIso8601String(),
    };
  }

  // Create Drift companion from Supabase JSON
  static IncomeCompanion fromJson(Map<String, dynamic> json) {
    // Helper function to safely convert numeric values to double
    double safeToDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    // Helper function to safely convert numeric values to int
    int safeToInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    return IncomeCompanion(
      uuid: Value(json['uuid'] as String),
      date: Value(safeDateTimeParse(json['date'] as String)),
      initialMileage: Value(safeToInt(json['initial_mileage'])),
      finalMileage: Value(safeToInt(json['final_mileage'])),
      initialGopay: Value(safeToDouble(json['initial_gopay'])),
      initialBca: Value(safeToDouble(json['initial_bca'])),
      initialCash: Value(safeToDouble(json['initial_cash'])),
      initialOvo: Value(safeToDouble(json['initial_ovo'])),
      initialBri: Value(safeToDouble(json['initial_bri'])),
      initialRekpon: Value(safeToDouble(json['initial_rekpon'])),
      finalGopay: Value(safeToDouble(json['final_gopay'])),
      finalBca: Value(safeToDouble(json['final_bca'])),
      finalCash: Value(safeToDouble(json['final_cash'])),
      finalOvo: Value(safeToDouble(json['final_ovo'])),
      finalBri: Value(safeToDouble(json['final_bri'])),
      finalRekpon: Value(safeToDouble(json['final_rekpon'])),
      initialCapital: json['initial_capital'] != null
          ? Value(safeToDouble(json['initial_capital']))
          : const Value(null),
      finalResult: json['final_result'] != null
          ? Value(safeToDouble(json['final_result']))
          : const Value(null),
      mileage: json['mileage'] != null
          ? Value(safeToInt(json['mileage']))
          : const Value(null),
      netIncome: json['net_income'] != null
          ? Value(safeToDouble(json['net_income']))
          : const Value(null),
      createdAt: Value(safeDateTimeParse(json['created_at'] as String)),
      updatedAt: Value(safeDateTimeParse(json['updated_at'] as String)),
      deletedAt: json['deleted_at'] != null
          ? Value(safeDateTimeParse(json['deleted_at'] as String))
          : const Value(null),
      syncStatus: const Value(SyncStatus.synced),
    );
  }
}

// Orders Sync Model
class OrdersSyncModel extends SyncModel {
  @override
  final String uuid;
  final DateTime date;
  final int orderCompleted;
  final int orderMissed;
  final int orderCanceled;
  final int cbsOrder;
  final int? incomingOrder;
  final int? orderReceived;
  final double? bidAcceptance;
  final double? tripCompletion;
  final int points;
  final double trip;
  final double bonus;
  final double tips;
  final double? income;
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final DateTime? deletedAt;
  @override
  final SyncStatus syncStatus;

  OrdersSyncModel({
    required this.uuid,
    required this.date,
    required this.orderCompleted,
    required this.orderMissed,
    required this.orderCanceled,
    required this.cbsOrder,
    this.incomingOrder,
    this.orderReceived,
    this.bidAcceptance,
    this.tripCompletion,
    required this.points,
    required this.trip,
    required this.bonus,
    required this.tips,
    this.income,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });

  // Factory method to create model from Drift entity
  factory OrdersSyncModel.fromDrift(Order data) {
    return OrdersSyncModel(
      uuid: data.uuid,
      date: data.date,
      orderCompleted: data.orderCompleted,
      orderMissed: data.orderMissed,
      orderCanceled: data.orderCanceled,
      cbsOrder: data.cbsOrder,
      incomingOrder: data.incomingOrder,
      orderReceived: data.orderReceived,
      bidAcceptance: data.bidAcceptance,
      tripCompletion: data.tripCompletion,
      points: data.points,
      trip: data.trip,
      bonus: data.bonus,
      tips: data.tips,
      income: data.income,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      syncStatus: data.syncStatus,
    );
  }

  // Convert to Supabase JSON format for upload
  @override
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'date': date.toUtc().toIso8601String(),
      'order_completed': orderCompleted,
      'order_missed': orderMissed,
      'order_canceled': orderCanceled,
      'cbs_order': cbsOrder,
      'incoming_order': incomingOrder,
      'order_received': orderReceived,
      'bid_acceptance': bidAcceptance,
      'trip_completion': tripCompletion,
      'points': points,
      'trip': trip,
      'bonus': bonus,
      'tips': tips,
      'income': income,
      'created_at': createdAt.toUtc().toIso8601String(),
      'updated_at': updatedAt.toUtc().toIso8601String(),
      'deleted_at': deletedAt?.toUtc().toIso8601String(),
    };
  }

  // Create Drift companion from Supabase JSON
  static OrdersCompanion fromJson(Map<String, dynamic> json) {
    // Helper function to safely convert numeric values to double
    double safeToDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    // Helper function to safely convert numeric values to int
    int safeToInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    return OrdersCompanion(
      uuid: Value(json['uuid'] as String),
      date: Value(safeDateTimeParse(json['date'] as String)),
      orderCompleted: Value(safeToInt(json['order_completed'])),
      orderMissed: Value(safeToInt(json['order_missed'])),
      orderCanceled: Value(safeToInt(json['order_canceled'])),
      cbsOrder: Value(safeToInt(json['cbs_order'])),
      incomingOrder: json['incoming_order'] != null
          ? Value(safeToInt(json['incoming_order']))
          : const Value(null),
      orderReceived: json['order_received'] != null
          ? Value(safeToInt(json['order_received']))
          : const Value(null),
      bidAcceptance: json['bid_acceptance'] != null
          ? Value(safeToDouble(json['bid_acceptance']))
          : const Value(null),
      tripCompletion: json['trip_completion'] != null
          ? Value(safeToDouble(json['trip_completion']))
          : const Value(null),
      points: Value(safeToInt(json['points'])),
      trip: Value(safeToDouble(json['trip'])),
      bonus: Value(safeToDouble(json['bonus'])),
      tips: Value(safeToDouble(json['tips'])),
      income: json['income'] != null
          ? Value(safeToDouble(json['income']))
          : const Value(null),
      createdAt: Value(safeDateTimeParse(json['created_at'] as String)),
      updatedAt: Value(safeDateTimeParse(json['updated_at'] as String)),
      deletedAt: json['deleted_at'] != null
          ? Value(safeDateTimeParse(json['deleted_at'] as String))
          : const Value(null),
      syncStatus: const Value(SyncStatus.synced),
    );
  }
}

// Performance Sync Model
class PerformanceSyncModel extends SyncModel {
  @override
  final String uuid;
  final DateTime date;
  final double bidPerformance;
  final double tripPerformance;
  final int activeDays;
  final double onlineHours;
  final double? avgCompleted;
  final double? avgOnline;
  final double? retention;
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final DateTime? deletedAt;
  @override
  final SyncStatus syncStatus;

  PerformanceSyncModel({
    required this.uuid,
    required this.date,
    required this.bidPerformance,
    required this.tripPerformance,
    required this.activeDays,
    required this.onlineHours,
    this.avgCompleted,
    this.avgOnline,
    this.retention,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });

  // Factory method to create model from Drift entity
  factory PerformanceSyncModel.fromDrift(PerformanceData data) {
    return PerformanceSyncModel(
      uuid: data.uuid,
      date: data.date,
      bidPerformance: data.bidPerformance,
      tripPerformance: data.tripPerformance,
      activeDays: data.activeDays,
      onlineHours: data.onlineHours,
      avgCompleted: data.avgCompleted,
      avgOnline: data.avgOnline,
      retention: data.retention,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      syncStatus: data.syncStatus,
    );
  }

  // Convert to Supabase JSON format for upload
  @override
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'date': date.toUtc().toIso8601String(),
      'bid_performance': bidPerformance,
      'trip_performance': tripPerformance,
      'active_days': activeDays,
      'online_hours': onlineHours,
      'avg_completed': avgCompleted,
      'avg_online': avgOnline,
      'retention': retention,
      'created_at': createdAt.toUtc().toIso8601String(),
      'updated_at': updatedAt.toUtc().toIso8601String(),
      'deleted_at': deletedAt?.toUtc().toIso8601String(),
    };
  }

  // Create Drift companion from Supabase JSON
  static PerformanceCompanion fromJson(Map<String, dynamic> json) {
    // Helper function to safely convert numeric values to double
    double safeToDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    // Helper function to safely convert numeric values to int
    int safeToInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    return PerformanceCompanion(
      uuid: Value(json['uuid'] as String),
      date: Value(safeDateTimeParse(json['date'] as String)),
      bidPerformance: Value(safeToDouble(json['bid_performance'])),
      tripPerformance: Value(safeToDouble(json['trip_performance'])),
      activeDays: Value(safeToInt(json['active_days'])),
      onlineHours: Value(safeToDouble(json['online_hours'])),
      avgCompleted: json['avg_completed'] != null
          ? Value(safeToDouble(json['avg_completed']))
          : const Value(null),
      avgOnline: json['avg_online'] != null
          ? Value(safeToDouble(json['avg_online']))
          : const Value(null),
      retention: json['retention'] != null
          ? Value(safeToDouble(json['retention']))
          : const Value(null),
      createdAt: Value(safeDateTimeParse(json['created_at'] as String)),
      updatedAt: Value(safeDateTimeParse(json['updated_at'] as String)),
      deletedAt: json['deleted_at'] != null
          ? Value(safeDateTimeParse(json['deleted_at'] as String))
          : const Value(null),
      syncStatus: const Value(SyncStatus.synced),
    );
  }
}

// Spare Parts Sync Model
class SparePartsSyncModel extends SyncModel {
  @override
  final String uuid;
  final String partName;
  final String partType;
  final double price;
  final int mileageLimit;
  final int initialMileage;
  final DateTime installationDate;
  final int currentMileage;
  final bool warningStatus;
  final int replacementCount;
  final String notes;
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final DateTime? deletedAt;
  @override
  final SyncStatus syncStatus;

  SparePartsSyncModel({
    required this.uuid,
    required this.partName,
    required this.partType,
    required this.price,
    required this.mileageLimit,
    required this.initialMileage,
    required this.installationDate,
    required this.currentMileage,
    required this.warningStatus,
    required this.replacementCount,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });

  // Factory method to create model from Drift entity
  factory SparePartsSyncModel.fromDrift(SparePart data) {
    return SparePartsSyncModel(
      uuid: data.uuid,
      partName: data.partName,
      partType: data.partType,
      price: data.price,
      mileageLimit: data.mileageLimit,
      initialMileage: data.initialMileage,
      installationDate: data.installationDate,
      currentMileage: data.currentMileage,
      warningStatus: data.warningStatus,
      replacementCount: data.replacementCount,
      notes: data.notes,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      syncStatus: data.syncStatus,
    );
  }

  // Convert to Supabase JSON format for upload
  @override
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'part_name': partName,
      'part_type': partType,
      'price': price,
      'mileage_limit': mileageLimit,
      'initial_mileage': initialMileage,
      'installation_date': installationDate.toUtc().toIso8601String(),
      'current_mileage': currentMileage,
      'warning_status': warningStatus,
      'replacement_count': replacementCount,
      'notes': notes,
      'created_at': createdAt.toUtc().toIso8601String(),
      'updated_at': updatedAt.toUtc().toIso8601String(),
      'deleted_at': deletedAt?.toUtc().toIso8601String(),
    };
  }

  // Create Drift companion from Supabase JSON
  static SparePartsCompanion fromJson(Map<String, dynamic> json) {
    // Helper function to safely convert numeric values to double
    double safeToDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    // Helper function to safely convert numeric values to int
    int safeToInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    return SparePartsCompanion(
      uuid: Value(json['uuid'] as String),
      partName: Value(json['part_name'] as String),
      partType: Value(json['part_type'] as String),
      price: Value(safeToDouble(json['price'])),
      mileageLimit: Value(safeToInt(json['mileage_limit'])),
      initialMileage: Value(safeToInt(json['initial_mileage'])),
      installationDate: Value(
        safeDateTimeParse(json['installation_date'] as String),
      ),
      currentMileage: Value(safeToInt(json['current_mileage'])),
      warningStatus: Value(json['warning_status'] as bool),
      replacementCount: Value(safeToInt(json['replacement_count'])),
      notes: Value(json['notes'] as String),
      createdAt: Value(safeDateTimeParse(json['created_at'] as String)),
      updatedAt: Value(safeDateTimeParse(json['updated_at'] as String)),
      deletedAt: json['deleted_at'] != null
          ? Value(safeDateTimeParse(json['deleted_at'] as String))
          : const Value(null),
      syncStatus: const Value(SyncStatus.synced),
    );
  }
}

// Spare Parts History Sync Model
class SparePartsHistorySyncModel extends SyncModel {
  @override
  final String uuid;
  final String partName;
  final String partType;
  final double price;
  final DateTime replacementDate;
  final int mileageAtReplacement;
  final int sparePartId;
  final DateTime installationDate;
  final int initialMileage;
  final String replacementReason;
  final int? replacedByPartId;
  final int replacementCount;
  final int usageDays;
  final int usageMileage;
  final String notes;
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final DateTime? deletedAt;
  @override
  final SyncStatus syncStatus;

  SparePartsHistorySyncModel({
    required this.uuid,
    required this.partName,
    required this.partType,
    required this.price,
    required this.replacementDate,
    required this.mileageAtReplacement,
    required this.sparePartId,
    required this.installationDate,
    required this.initialMileage,
    required this.replacementReason,
    this.replacedByPartId,
    required this.replacementCount,
    required this.usageDays,
    required this.usageMileage,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });

  // Factory method to create model from Drift entity
  factory SparePartsHistorySyncModel.fromDrift(SparePartsHistoryData data) {
    return SparePartsHistorySyncModel(
      uuid: data.uuid,
      partName: data.partName,
      partType: data.partType,
      price: data.price,
      replacementDate: data.replacementDate,
      mileageAtReplacement: data.mileageAtReplacement,
      sparePartId: data.sparePartId,
      installationDate: data.installationDate,
      initialMileage: data.initialMileage,
      replacementReason: data.replacementReason,
      replacedByPartId: data.replacedByPartId,
      replacementCount: data.replacementCount,
      usageDays: data.usageDays,
      usageMileage: data.usageMileage,
      notes: data.notes,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      syncStatus: data.syncStatus,
    );
  }

  // Convert to Supabase JSON format for upload
  @override
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'part_name': partName,
      'part_type': partType,
      'price': price,
      'replacement_date': replacementDate.toUtc().toIso8601String(),
      'mileage_at_replacement': mileageAtReplacement,
      'spare_part_id': sparePartId,
      'installation_date': installationDate.toUtc().toIso8601String(),
      'initial_mileage': initialMileage,
      'replacement_reason': replacementReason,
      'replaced_by_part_id': replacedByPartId,
      'replacement_count': replacementCount,
      'usage_days': usageDays,
      'usage_mileage': usageMileage,
      'notes': notes,
      'created_at': createdAt.toUtc().toIso8601String(),
      'updated_at': updatedAt.toUtc().toIso8601String(),
      'deleted_at': deletedAt?.toUtc().toIso8601String(),
    };
  }

  // Create Drift companion from Supabase JSON
  static SparePartsHistoryCompanion fromJson(Map<String, dynamic> json) {
    // Helper function to safely convert numeric values to double
    double safeToDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    // Helper function to safely convert numeric values to int
    int safeToInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    return SparePartsHistoryCompanion(
      uuid: Value(json['uuid'] as String),
      partName: Value(json['part_name'] as String),
      partType: Value(json['part_type'] as String),
      price: Value(safeToDouble(json['price'])),
      replacementDate: Value(
        safeDateTimeParse(json['replacement_date'] as String),
      ),
      mileageAtReplacement: Value(safeToInt(json['mileage_at_replacement'])),
      sparePartId: Value(safeToInt(json['spare_part_id'])),
      installationDate: Value(
        safeDateTimeParse(json['installation_date'] as String),
      ),
      initialMileage: Value(safeToInt(json['initial_mileage'])),
      replacementReason: Value(json['replacement_reason'] as String),
      replacedByPartId: json['replaced_by_part_id'] != null
          ? Value(safeToInt(json['replaced_by_part_id']))
          : const Value(null),
      replacementCount: Value(safeToInt(json['replacement_count'])),
      usageDays: Value(safeToInt(json['usage_days'])),
      usageMileage: Value(safeToInt(json['usage_mileage'])),
      notes: Value(json['notes'] as String),
      createdAt: Value(safeDateTimeParse(json['created_at'] as String)),
      updatedAt: Value(safeDateTimeParse(json['updated_at'] as String)),
      deletedAt: json['deleted_at'] != null
          ? Value(safeDateTimeParse(json['deleted_at'] as String))
          : const Value(null),
      syncStatus: const Value(SyncStatus.synced),
    );
  }
}
