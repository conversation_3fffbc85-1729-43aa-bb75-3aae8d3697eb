import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../providers/global_date_range_provider.dart';

/// Optimized date range display widget that only rebuilds when the actual date range changes
class OptimizedDateRangeDisplay extends ConsumerWidget {
  final Color? textColor;
  final Color? backgroundColor;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;

  const OptimizedDateRangeDisplay({
    super.key,
    this.textColor,
    this.backgroundColor,
    this.padding,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Optimize: Only rebuild when the actual DateTimeRange data changes,
    // not when loading state changes
    final dateRange = ref.watch(
      globalDateRangeProvider.select((asyncValue) {
        return asyncValue.whenOrNull(data: (range) => range);
      }),
    );

    if (dateRange == null) {
      // Show loading indicator or placeholder
      return Container(
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.white.withValues(alpha: 0.9),
          borderRadius: borderRadius ?? BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Loading...',
              style: TextStyle(
                color: textColor ?? Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            Icon(
              Icons.date_range,
              color: textColor ?? Theme.of(context).primaryColor,
            ),
          ],
        ),
      );
    }

    return Container(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white.withValues(alpha: 0.9),
        borderRadius: borderRadius ?? BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${dateRange.start.day}/${dateRange.start.month}/${dateRange.start.year} - ${dateRange.end.day}/${dateRange.end.month}/${dateRange.end.year}',
            style: TextStyle(
              color: textColor ?? Theme.of(context).primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          Icon(
            Icons.date_range,
            color: textColor ?? Theme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }
}

/// Optimized loading state checker that only listens to loading state changes
class OptimizedLoadingStateChecker extends ConsumerWidget {
  final List<AsyncValue> asyncValues;
  final Widget Function(bool isLoading) builder;

  const OptimizedLoadingStateChecker({
    super.key,
    required this.asyncValues,
    required this.builder,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Optimize: Only rebuild when loading states actually change
    final isLoading = asyncValues.any((asyncValue) => asyncValue.isLoading);
    return builder(isLoading);
  }
}

/// Mixin for optimized provider watching
mixin OptimizedProviderWatching<T extends ConsumerWidget> on ConsumerWidget {
  /// Watch only the data from an AsyncValue, ignoring loading/error state changes
  R? watchData<R>(WidgetRef ref, ProviderListenable<AsyncValue<R>> provider) {
    return ref.watch(
      provider.select((asyncValue) => asyncValue.whenOrNull(data: (data) => data)),
    );
  }

  /// Watch only the loading state from an AsyncValue
  bool watchLoading(WidgetRef ref, ProviderListenable<AsyncValue> provider) {
    return ref.watch(
      provider.select((asyncValue) => asyncValue.isLoading),
    );
  }

  /// Watch only the error state from an AsyncValue
  Object? watchError(WidgetRef ref, ProviderListenable<AsyncValue> provider) {
    return ref.watch(
      provider.select((asyncValue) => asyncValue.whenOrNull(error: (error, _) => error)),
    );
  }
}
