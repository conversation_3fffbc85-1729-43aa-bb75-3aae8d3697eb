import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/auth/auth_logger.dart';
import '../../../../core/services/sync/sync_logger.dart';
import '../../../../core/theme/app_colors.dart';

/// Enum for log types
enum LogType {
  /// Sync logs
  sync,

  /// Authentication logs
  auth,
}

/// Widget for displaying log filter controls
class LogFilterBar extends ConsumerWidget {
  /// The currently selected log type
  final LogType selectedLogType;

  /// Constructor
  const LogFilterBar({required this.selectedLogType, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (selectedLogType == LogType.sync) {
      final logLevelFilter = ref.watch(logLevelFilterProvider);
      return _buildSyncLogLevelSelector(context, ref, logLevelFilter);
    } else {
      final importanceFilter = ref.watch(authLogImportanceFilterProvider);
      return _buildAuthLogImportanceSelector(context, ref, importanceFilter);
    }
  }

  /// Builds the sync log level selector
  Widget _buildSyncLogLevelSelector(
    BuildContext context,
    WidgetRef ref,
    LogLevel logLevelFilter,
  ) {
    return InkWell(
      onTap: () => _showSyncLogLevelDialog(context, ref, logLevelFilter),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20.0),
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.filter_list, size: 16.0, color: AppColors.primary),
            const SizedBox(width: 4.0),
            Text(
              'Level: ${logLevelFilter.name}',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the auth log importance selector
  Widget _buildAuthLogImportanceSelector(
    BuildContext context,
    WidgetRef ref,
    AuthLogImportance importanceFilter,
  ) {
    return InkWell(
      onTap: () => _showAuthLogImportanceDialog(context, ref, importanceFilter),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20.0),
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.filter_list, size: 16.0, color: AppColors.primary),
            const SizedBox(width: 4.0),
            Text(
              'Importance: ${importanceFilter.name}',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Shows the sync log level filter dialog
  void _showSyncLogLevelDialog(
    BuildContext context,
    WidgetRef ref,
    LogLevel currentLevel,
  ) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Log Level'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: LogLevel.values.map((level) {
            return RadioListTile<LogLevel>(
              title: Text(level.name),
              value: level,
              groupValue: currentLevel,
              onChanged: (LogLevel? value) {
                if (value != null) {
                  ref.read(logLevelFilterProvider.notifier).state = value;
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Shows the auth log importance filter dialog
  void _showAuthLogImportanceDialog(
    BuildContext context,
    WidgetRef ref,
    AuthLogImportance currentImportance,
  ) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Importance'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AuthLogImportance.values.map((importance) {
            return RadioListTile<AuthLogImportance>(
              title: Text(importance.name),
              value: importance,
              groupValue: currentImportance,
              onChanged: (AuthLogImportance? value) {
                if (value != null) {
                  ref.read(authLogImportanceFilterProvider.notifier).state =
                      value;
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
