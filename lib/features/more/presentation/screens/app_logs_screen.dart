import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/auth/auth_logger.dart';
import '../../../../core/services/auth/auth_logger_provider.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../widgets/log_filter_bar.dart';
import '../widgets/log_list_view.dart';

/// Provider for the selected log type
final selectedLogTypeProvider = StateProvider<LogType>((ref) => LogType.sync);

/// A unified screen that displays both sync and authentication logs
class AppLogsScreen extends ConsumerWidget {
  /// Constructor
  const AppLogsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedLogType = ref.watch(selectedLogTypeProvider);

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh logs based on selected type
          if (selectedLogType == LogType.sync) {
            ref.read(syncServiceProvider).refreshLogs();
          } else {
            // For auth logs, just trigger a UI refresh
            ref.invalidate(authLogsProvider);
          }
        },
        child: CustomScrollView(
          slivers: [
            _buildAppBar(context, ref, selectedLogType),

            // Show logs based on selected type
            if (selectedLogType == LogType.sync)
              const SyncLogListView()
            else
              const AuthLogListView(),
          ],
        ),
      ),
    );
  }

  /// Builds the app bar with log type selector
  SliverAppBar _buildAppBar(
    BuildContext context,
    WidgetRef ref,
    LogType selectedLogType,
  ) {
    return SliverAppBar(
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      title: Text('${selectedLogType == LogType.sync ? 'Sync' : 'Auth'} Logs'),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 12.0),
          child: LogFilterBar(selectedLogType: selectedLogType),
        ),
      ),
      actions: [
        // Log type selector
        IconButton(
          icon: Icon(
            selectedLogType == LogType.sync ? Icons.security : Icons.sync,
          ),
          tooltip:
              'Switch to ${selectedLogType == LogType.sync ? 'Auth' : 'Sync'} Logs',
          onPressed: () {
            ref.read(selectedLogTypeProvider.notifier).state =
                selectedLogType == LogType.sync ? LogType.auth : LogType.sync;
          },
        ),
        // Clear logs button
        IconButton(
          icon: const Icon(Icons.delete_sweep),
          tooltip: 'Clear logs',
          onPressed: () {
            _clearLogs(ref, selectedLogType);
          },
        ),
      ],
    );
  }

  /// Clear logs based on selected type
  void _clearLogs(WidgetRef ref, LogType selectedLogType) {
    if (selectedLogType == LogType.sync) {
      ref.read(syncServiceProvider).clearLogs();
    } else {
      // Clear auth logs
      ref.read(authLoggerProvider).clearLogs();
    }
  }
}
