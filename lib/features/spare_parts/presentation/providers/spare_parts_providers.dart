import 'package:flutter/material.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/providers/app_settings_provider.dart';
import '../../../../core/services/sync_service.dart';
import '../../data/repositories/spare_parts_repository_impl.dart';
import '../../domain/entities/replacement_history.dart';
import '../../domain/entities/spare_part.dart';
import '../../domain/repositories/spare_parts_repository.dart';
import '../../domain/use_cases/add_spare_part.dart';
import '../../domain/use_cases/get_replacement_history.dart';
import '../../domain/use_cases/get_spare_part.dart';
import '../../domain/use_cases/get_spare_parts.dart';
import '../../domain/use_cases/replace_spare_part.dart';

part 'spare_parts_providers.g.dart';

// Provider for enhanced spare parts repository
@riverpod
EnhancedSparePartsRepository enhancedSparePartsRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  final syncService = ref.watch(syncServiceProvider);
  return EnhancedSparePartsRepositoryImpl(
    database: database,
    syncService: syncService,
  );
}

// Provider for get enhanced spare parts use case
@riverpod
GetEnhancedSpareParts getEnhancedSpareParts(Ref ref) {
  final repository = ref.watch(enhancedSparePartsRepositoryProvider);
  return GetEnhancedSpareParts(repository);
}

// Provider for get enhanced spare part use case
@riverpod
GetEnhancedSparePart getEnhancedSparePart(Ref ref) {
  final repository = ref.watch(enhancedSparePartsRepositoryProvider);
  return GetEnhancedSparePart(repository);
}

// Provider for add enhanced spare part use case
@riverpod
AddEnhancedSparePart addEnhancedSparePart(Ref ref) {
  final repository = ref.watch(enhancedSparePartsRepositoryProvider);
  return AddEnhancedSparePart(repository);
}

// Provider for replace enhanced spare part use case
@riverpod
ReplaceEnhancedSparePart replaceEnhancedSparePart(Ref ref) {
  final repository = ref.watch(enhancedSparePartsRepositoryProvider);
  return ReplaceEnhancedSparePart(repository);
}

// Provider for get enhanced replacement history use case
@riverpod
GetEnhancedReplacementHistory getEnhancedReplacementHistory(Ref ref) {
  final repository = ref.watch(enhancedSparePartsRepositoryProvider);
  return GetEnhancedReplacementHistory(repository);
}

// Provider for enhanced spare parts list
@riverpod
Future<List<EnhancedSparePart>> enhancedSparePartsList(Ref ref) async {
  final getSparePartsUseCase = ref.watch(getEnhancedSparePartsProvider);
  final result = await getSparePartsUseCase.execute();

  return result.fold((failure) {
    debugPrint('Error fetching spare parts: ${failure.toString()}');
    return [];
  }, (spareParts) => spareParts);
}

// Provider for enhanced spare part by ID
@riverpod
Future<EnhancedSparePart?> enhancedSparePart(Ref ref, int id) async {
  final getSparePartUseCase = ref.watch(getEnhancedSparePartProvider);
  final result = await getSparePartUseCase.execute(id);

  return result.fold((failure) {
    debugPrint('Error fetching spare part: ${failure.toString()}');
    return null;
  }, (sparePart) => sparePart);
}

// Provider for enhanced replacement history
@riverpod
Future<List<EnhancedReplacementHistory>> enhancedReplacementHistory(
  Ref ref,
  int sparePartId,
) async {
  final getHistoryUseCase = ref.watch(getEnhancedReplacementHistoryProvider);
  final result = await getHistoryUseCase.execute(sparePartId);

  return result.fold((failure) {
    debugPrint('Error fetching replacement history: ${failure.toString()}');
    return [];
  }, (history) => history);
}

// Provider for add spare part operation
@riverpod
class AddSparePartOperation extends _$AddSparePartOperation {
  @override
  Future<EnhancedSparePart?> build() async {
    return null; // Initially no operation
  }

  Future<void> addSparePart({
    required String partName,
    required String partType,
    required double price,
    required int mileageLimit,
    required DateTime installationDate,
    int? initialMileage,
    String notes = '',
  }) async {
    state = const AsyncValue.loading();

    final addSparePartUseCase = ref.read(addEnhancedSparePartProvider);
    final result = await addSparePartUseCase.execute(
      partName: partName,
      partType: partType,
      price: price,
      mileageLimit: mileageLimit,
      installationDate: installationDate,
      initialMileage: initialMileage,
      notes: notes,
    );

    state = result.fold(
      (failure) => AsyncValue.error(failure.toString(), StackTrace.current),
      (sparePart) => AsyncValue.data(sparePart),
    );

    // Refresh the spare parts list
    ref.invalidate(enhancedSparePartsListProvider);
  }
}

// Provider for replace spare part operation
@riverpod
class ReplaceSparePartOperation extends _$ReplaceSparePartOperation {
  @override
  Future<ReplacementResult?> build() async {
    return null; // Initially no operation
  }

  Future<void> replaceSparePart({
    required int sparePartId,
    required DateTime replacementDate,
    required double newPartPrice,
    required int currentMileage,
    String? newPartName,
    String? newPartType,
    int? newMileageLimit,
    String replacementReason = 'Regular maintenance',
    String notes = '',
  }) async {
    state = const AsyncValue.loading();

    final replaceSparePartUseCase = ref.read(replaceEnhancedSparePartProvider);
    final result = await replaceSparePartUseCase.execute(
      sparePartId: sparePartId,
      replacementDate: replacementDate,
      newPartPrice: newPartPrice,
      newPartName: newPartName,
      newPartType: newPartType,
      newMileageLimit: newMileageLimit,
      currentMileage: currentMileage,
      replacementReason: replacementReason,
      notes: notes,
    );

    state = result.fold(
      (failure) => AsyncValue.error(failure.toString(), StackTrace.current),
      (replacementResult) => AsyncValue.data(replacementResult),
    );

    // Refresh the spare parts list
    ref.invalidate(enhancedSparePartsListProvider);

    // Also refresh the replacement history for this spare part
    result.fold(
      (failure) => {}, // Do nothing on failure
      (replacementResult) {
        debugPrint(
          'Invalidating replacement history provider for spare part ID: ${replacementResult.sparePart.id}',
        );
        ref.invalidate(
          enhancedReplacementHistoryProvider(replacementResult.sparePart.id!),
        );
      },
    );
  }
}

// Provider for delete spare part operation
@riverpod
class DeleteSparePartOperation extends _$DeleteSparePartOperation {
  @override
  Future<bool?> build() async {
    return null; // Initially no operation
  }

  Future<void> deleteSparePart(int sparePartId) async {
    state = const AsyncValue.loading();

    final repository = ref.read(enhancedSparePartsRepositoryProvider);
    final result = await repository.deleteSparePart(sparePartId);

    state = result.fold(
      (failure) => AsyncValue.error(failure.toString(), StackTrace.current),
      (success) => AsyncValue.data(success),
    );

    // Refresh the spare parts list
    ref.invalidate(enhancedSparePartsListProvider);
  }
}
